🧾 Źródło danych – segments.csv

Twój plik CSV zawiera strukturę kategorii i przypisanie do segmentów cenowych. Zawiera on następujące kluczowe kolumny:

Kolumna	Opis
country_code	Kod kraju (se, no, fi itd.)
category_id	ID kategorii leaf (końcowej)
category_name	Nazwa leaf kategorii w języku lokalnym
path	Pełna ścieżka kategorii, np. `"1177
visible	Czy kategoria jest widoczna (TRUE/FALSE)
status	Status techniczny kategorii
id_part	Część path zawierająca tylko ID (przed znakiem ¤)
main_category_id	ID kategorii głównej (segmentu kategorii)
parent_id	ID kategorii obejmującej (parent category)
old_segment_id	Historyczny ID segmentu cenowego (niewykorzystywany)
en_main_category	Nazwa główna kategorii (segment) po angielsku
en_parent_name	Nazwa parent kategorii po angielsku
en_category_name	Nazwa leaf kategorii po angielsku
segment_name	Nazwa segmentu cenowego (np. "Segment 12")
clicks	Liczba kliknięć (używana do filtrowania i analizy)
🔍 Co robi program

Program w Streamlit wykonuje analizę struktury kategorii i ich przypisania do segmentów cenowych. Działa w kilku krokach:

1️⃣ Filtrowanie danych
Użytkownik wybiera:

kraj (z flagami),
język nazw (Angielski lub Lokalny),
próg agregacji kategorii (0–100%),
minimalną liczbę klików, które muszą mieć kategorie, by były brane pod uwagę.
2️⃣ Agregacja kategorii (dwupoziomowa)
Program tworzy 2 poziomy agregacji:

A. Leafy → Parent

Jeśli wszystkie leafy pod danym parentem są przypisane do tego samego segmentu – parent jest agregowany.
Jeśli tylko np. 80% pasuje, parent otrzymuje *, a wyjątki (pozostałe 20%) są widoczne jako osobne leafy.

B. Parenty → Main (segment kategorii)

Analogicznie, jeśli wszystkie parenty przypisane do głównej kategorii (main) pasują do jednego segmentu – main jest agregowany.
Jeśli nie – widoczne są parenty i wyjątki.

3️⃣ Wyświetlanie danych w dwóch widokach
✅ Tabela 1 – Szczegółowa (st.dataframe)

Pokazuje każdy wiersz: category, level, segment, clicks, note, aggregated
✅ Tabela 2 – Pivot segmentów (segment_name jako kolumny)

Kategorie w kolumnach odpowiadających segmentom
Leafy i parenty są oznaczone kolorami:
🔵 parent → tło niebieskie
⚠️ wyjątek → tło czerwone
★ częściowa agregacja → tło żółte
pogrubienie dla mainów
Sortowanie alfabetyczne w kolumnach
Kolejność segmentów poprawiona: 1,2,3... (nie 1,10,11)
4️⃣ Analiza wybranej kategorii (drilldown)
Po kliknięciu lub wyborze z listy kategorii:

jeśli to main → pokazuje wszystkie parenty i leafy pod nim
jeśli to parent → pokazuje wszystkie leafy pod nim
wykres kołowy pokazuje rozkład przypisania leafów do segmentów cenowych
liczony wg liczby leafów (nie wierszy)
z etykietami „Segment X (5 kategorii)”
✅ Dodatkowe funkcje i założenia

Funkcja	Status	Uwagi
Interaktywne filtry (kraj, kliknięcia, język, próg)	✅	Wszystko działa
Klikalna analiza kategorii	✅	Wybór z listy rozwijanej (selectbox)
Obsługa błędów typów i braków	✅	Automatyczne konwertowanie clicks, ignorowanie błędów
Wykresy w kolumnie	✅	Mniejszy wykres po prawej stronie, wycentrowany
Obsługa wyjątków (exception)	✅	Pokazywane w osobnych wierszach
Segmentacja dokładna (leaf → parent → main)	✅	Dwupoziomowa logika agregacji
🔁 Co jeszcze możesz chcieć dodać później:

📥 Eksport CSV tylko z widoku szczegółowego (po kliknięciu)
📊 Słupkowy wykres zamiast kołowego
📎 Możliwość klikania bezpośrednio w komórki tabeli pivot
📄 Eksport raportu do PDF lub XLSX
📈 Wykresy wg sumy kliknięć, nie liczby kategorii