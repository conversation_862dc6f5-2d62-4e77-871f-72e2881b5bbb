import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt
import os

st.set_page_config(page_title="Raport segmentów cenowych", layout="wide")
st.title("📊 Raport segmentów cenowych")

# === AGREGACJA DWUPOZIOMOWA ===
def aggregate_categories(df, threshold_leaf=0.8, threshold_parent=0.5, language="Angielski"):
    leaf_output = []

    grouped_leaf = df.groupby(['main_category_id', 'parent_id'])

    for (main_id, parent_id), group in grouped_leaf:
        total = len(group)

        # Sprawdź czy są jakiekolwiek segmenty w grupie
        valid_segments = group['segment_name'].dropna()
        if len(valid_segments) == 0:
            continue  # Pomiń grupy bez segmentów

        segment_mode = valid_segments.mode()
        if len(segment_mode) == 0:
            continue  # Pomiń jeśli nie ma dominującego segmentu

        dominant_segment = segment_mode.iloc[0]
        dominant_count = (group['segment_name'] == dominant_segment).sum()
        ratio = dominant_count / total

        sample = group.iloc[0]
        country = sample.get('country_code', 'XX')
        main_label = sample.get('en_main_category', 'UNKNOWN')

        parent_name = (
            sample.get('en_parent_name', 'UNKNOWN') if language == "Angielski"
            else sample.get('category_name', sample.get('en_parent_name', 'UNKNOWN'))
        )

        if ratio == 1.0:
            leaf_output.append({
                'country_code': country,
                'segment_category': main_label,
                'category': parent_name,
                'level': 'parent',
                'segment_name': dominant_segment,
                'aggregated': 'Yes',
                'note': f'100% ({dominant_count}/{total})',
                'parent_id': parent_id,
                'clicks': group['clicks'].sum()
            })
        elif ratio >= threshold_leaf:
            leaf_output.append({
                'country_code': country,
                'segment_category': main_label,
                'category': parent_name + ' *',
                'level': 'parent',
                'segment_name': dominant_segment,
                'aggregated': 'Partial',
                'note': f'{int(ratio * 100)}% ({dominant_count}/{total})',
                'parent_id': parent_id,
                'clicks': group[group['segment_name'] == dominant_segment]['clicks'].sum()
            })

            exceptions = group[group['segment_name'] != dominant_segment]
            for _, row in exceptions.iterrows():
                category_name = (
                    row.get('en_category_name', 'UNKNOWN') if language == "Angielski"
                    else row.get('category_name', row.get('en_category_name', 'UNKNOWN'))
                )
                leaf_output.append({
                    'country_code': row.get('country_code', 'XX'),
                    'segment_category': row.get('en_main_category', 'UNKNOWN'),
                    'category': category_name,
                    'level': 'leaf',
                    'segment_name': row.get('segment_name', 'UNKNOWN'),
                    'aggregated': 'No',
                    'note': 'exception',
                    'parent_id': parent_id,
                    'clicks': row.get('clicks', 0)
                })
        else:
            for _, row in group.iterrows():
                category_name = (
                    row.get('en_category_name', 'UNKNOWN') if language == "Angielski"
                    else row.get('category_name', row.get('en_category_name', 'UNKNOWN'))
                )
                leaf_output.append({
                    'country_code': row.get('country_code', 'XX'),
                    'segment_category': row.get('en_main_category', 'UNKNOWN'),
                    'category': category_name,
                    'level': 'leaf',
                    'segment_name': row.get('segment_name', 'UNKNOWN'),
                    'aggregated': 'No',
                    'note': '',
                    'parent_id': parent_id,
                    'clicks': row.get('clicks', 0)
                })

    leaf_df = pd.DataFrame(leaf_output)

    # === DRUGI POZIOM: parent → segment_category ===
    grouped_parent = leaf_df[leaf_df['level'] == 'parent'].groupby('segment_category')

    final_output = []

    for main_cat, group in grouped_parent:
        total = len(group)

        # Sprawdź czy są jakiekolwiek segmenty w grupie
        valid_segments = group['segment_name'].dropna()
        if len(valid_segments) == 0:
            continue  # Pomiń grupy bez segmentów

        segment_mode = valid_segments.mode()
        if len(segment_mode) == 0:
            continue  # Pomiń jeśli nie ma dominującego segmentu

        dominant_segment = segment_mode.iloc[0]
        dominant_count = (group['segment_name'] == dominant_segment).sum()
        ratio = dominant_count / total

        if ratio == 1.0:
            final_output.append({
                'country_code': group.iloc[0]['country_code'],
                'segment_category': main_cat,
                'category': main_cat,
                'level': 'main',
                'segment_name': dominant_segment,
                'aggregated': 'Yes',
                'note': f'100% ({dominant_count}/{total})',
                'parent_id': None,
                'clicks': group['clicks'].sum()
            })
        elif ratio >= threshold_parent:
            final_output.append({
                'country_code': group.iloc[0]['country_code'],
                'segment_category': main_cat,
                'category': main_cat + ' *',
                'level': 'main',
                'segment_name': dominant_segment,
                'aggregated': 'Partial',
                'note': f'{int(ratio * 100)}% ({dominant_count}/{total})',
                'parent_id': None,
                'clicks': group[group['segment_name'] == dominant_segment]['clicks'].sum()
            })
            final_output.extend(group[group['segment_name'] != dominant_segment].to_dict(orient='records'))
        else:
            final_output.extend(group.to_dict(orient='records'))

    final_output.extend(leaf_df[leaf_df['level'] == 'leaf'].to_dict(orient='records'))

    return pd.DataFrame(final_output)



# === WGRYWANIE DANYCH ===
csv_path = os.path.join(os.path.dirname(__file__), 'segments.csv')

try:
    df = pd.read_csv(csv_path)

    df['clicks'] = pd.to_numeric(df['clicks'], errors='coerce')
    df = df[df['clicks'].notna()]

    st.success("✅ Załadowano dane z pliku `segments.csv`")

    # === FILTRY ===
    flag_map = {
        'se': '🇸🇪', 'no': '🇳🇴', 'fi': '🇫🇮', 'dk': '🇩🇰',
        'gb': '🇬🇧', 'fr': '🇫🇷', 'nz': '🇳🇿'
    }

    country_codes = sorted(df['country_code'].dropna().unique())
    default_country = "se"
    default_index = country_codes.index(default_country) if default_country in country_codes else 0
    country_labels = [f"{flag_map.get(code.lower(), '')} {code.upper()}" for code in country_codes]

    row1_col1, row1_col2 = st.columns(2)
    with row1_col1:
        selected_label = st.selectbox("🌍 Wybierz kraj", options=country_labels, index=default_index)
        selected_country = selected_label.split()[-1].lower()

    with row1_col2:
        language = st.selectbox("🌐 Język wyświetlanych nazw", ["Angielski", "Lokalny"])

    row2_col1, row2_col2, row2_col3 = st.columns(3)
    with row2_col1:
        threshold_leaf = st.slider("📊 Próg agregacji kategorii końcowych (%)", 0, 100, 50, step=5) / 100
    with row2_col2:
        threshold_parent = st.slider("📈 Próg agregacji kategorii obejmujących (%)", 0, 100, 50, step=5) / 100
    with row2_col3:
        min_clicks = st.number_input("🔍 Minimalna liczba klików", min_value=0, value=1000, step=100)

    filtered_df = df[(df['country_code'] == selected_country) & (df['clicks'] >= min_clicks)]

    result_df = aggregate_categories(filtered_df, threshold_leaf, threshold_parent, language)

    st.markdown("### 🧾 Szczegółowy widok")
    st.dataframe(result_df, use_container_width=True)

    st.markdown("### 🧮 Widok segmentów jako kolumn")

    pivot = result_df.copy()
    pivot['segment_number'] = (
        pivot['segment_name'].astype(str).str.extract(r'(\d+)')[0].astype(float)
    )
    pivot = pivot.sort_values('segment_number')

    def format_label(row):
        level = row.get("level", "")
        label = f"{row.get('category', '')} ({level})"
        note = str(row.get('note', '')).strip()
        if level == "main" and "100%" in note:
            label += " ✅"
        elif level == "main":
            label += " ★ " + note
        elif level == "parent" and "*" in row.get("category", ""):
            label += " ★"
        elif "exception" in note:
            label += " ❗"
        return label

    pivot['label'] = pivot.apply(format_label, axis=1)

    pivot_grouped = (
        pivot.groupby('segment_name')['label']
        .apply(lambda labels: sorted(labels, key=lambda x: str(x).casefold()))
        .to_dict()
    )

    sorted_segments = sorted(
        pivot_grouped.keys(),
        key=lambda x: int(''.join(filter(str.isdigit, str(x))) or 0)
    )

    max_rows = max(len(v) for v in pivot_grouped.values()) if pivot_grouped else 0
    formatted_columns = {}
    for seg in sorted_segments:
        items = pivot_grouped.get(seg, [])
        formatted_columns[seg] = items + [''] * (max_rows - len(items))

    final_pivot_df = pd.DataFrame(formatted_columns)

    def highlight_cells(val):
        if isinstance(val, str):
            style = ""
            if "✅" in val:
                style += "background-color: #cce5ff;"
            elif "★" in val:
                style += "background-color: #fff4b2;"
            elif "❗" in val:
                style += "background-color: #ffcccc;"
            if "(main)" in val:
                style += "font-weight: bold;"
            return style
        return ""

    styled_df = final_pivot_df.style.applymap(highlight_cells)
    st.dataframe(styled_df, use_container_width=True, height=700)

    # === INTERAKTYWNA TABELA SZCZEGÓŁÓW ===
    st.markdown("### 🔎 Kliknij kategorię powyżej, by zobaczyć szczegóły")
    clicked = st.selectbox("🔍 Wybierz kategorię do analizy:", sorted(result_df['category'].unique()))

    if clicked:
        selected_clean = clicked.replace(" ★", "").replace(" ✅", "").split(" (")[0].strip()

        # Spróbuj dopasować do 'main'
        selected_main = result_df[
            (result_df['category'].str.startswith(selected_clean)) &
            (result_df['level'] == 'main')
        ]

        # Jeśli nie znajdziesz w main → szukaj jako parent
        if not selected_main.empty:
            main_cat = selected_main.iloc[0]['segment_category']
            parent_ids = result_df[
                (result_df['segment_category'] == main_cat) &
                (result_df['level'] == 'parent')
            ]['parent_id'].dropna().unique()

            related = result_df[
                (result_df['segment_category'] == main_cat) &
                (result_df['parent_id'].isin(parent_ids))
            ]
        else:
            parent_row = result_df[
                (result_df['category'].str.startswith(selected_clean)) &
                (result_df['level'] == 'parent')
            ]

            if not parent_row.empty:
                parent_id = parent_row.iloc[0]['parent_id']
                main_cat = parent_row.iloc[0]['segment_category']
                related = result_df[
                    (result_df['parent_id'] == parent_id) |
                    ((result_df['level'] == 'parent') & (result_df['parent_id'] == parent_id))
                ]
            else:
                related = pd.DataFrame()

        if not related.empty:
            st.markdown(f"### 📊 Szczegóły dla: **{selected_clean}**")

            left_col, right_col = st.columns([3, 1])

            with left_col:
                st.subheader("🗂️ Kategorie powiązane")

                # Dodaj lepsze oznaczenia pochodzenia kategorii
                display_df = related.copy()

                def format_category_origin(row):
                    category = row['category']
                    level = row['level']
                    aggregated = row['aggregated']
                    note = row.get('note', '')

                    if level == 'main':
                        if aggregated == 'Yes':
                            return f"🏢 {category} [MAIN: 100% zagregowana]"
                        elif aggregated == 'Partial':
                            return f"🏢 {category} [MAIN: częściowo zagregowana - {note}]"
                        else:
                            return f"🏢 {category} [MAIN: niezagregowana]"

                    elif level == 'parent':
                        if aggregated == 'Yes':
                            return f"📁 {category} [PARENT: 100% zagregowany - {note}]"
                        elif aggregated == 'Partial':
                            return f"📁 {category} [PARENT: częściowo zagregowany - {note}]"
                        else:
                            return f"📁 {category} [PARENT: niezagregowany]"

                    elif level == 'leaf':
                        if 'exception' in note:
                            return f"📄 {category} [LEAF: wyjątek z parent]"
                        else:
                            return f"📄 {category} [LEAF: poniżej progu parent]"

                    return category

                display_df['Kategoria (pochodzenie)'] = display_df.apply(format_category_origin, axis=1)

                # Wyświetl z lepszymi kolumnami
                columns_to_show = ['Kategoria (pochodzenie)', 'segment_name', 'clicks', 'aggregated']
                st.dataframe(
                    display_df[columns_to_show].rename(columns={
                        'segment_name': 'Segment',
                        'clicks': 'Kliki',
                        'aggregated': 'Status agregacji'
                    }),
                    use_container_width=True
                )

            with right_col:
                st.subheader("📈 Rozkład segmentów")

                if not related.empty:
                    # Analiza rozkładu kategorii według segmentów - uwzględniamy rzeczywistą liczbę kategorii
                    segment_counts = {}

                    for _, row in related.iterrows():
                        segment = row['segment_name']

                        if row['aggregated'] == 'Yes':
                            # 100% agregacja - wszystkie kategorie w tym parent należą do tego segmentu
                            note = row.get('note', '')
                            if '(' in note and '/' in note:
                                try:
                                    total_in_parent = int(note.split('(')[1].split('/')[1].split(')')[0])
                                    segment_counts[segment] = segment_counts.get(segment, 0) + total_in_parent
                                except:
                                    segment_counts[segment] = segment_counts.get(segment, 0) + 1
                            else:
                                segment_counts[segment] = segment_counts.get(segment, 0) + 1

                        elif row['aggregated'] == 'Partial':
                            # Częściowa agregacja - tylko część kategorii należy do tego segmentu
                            note = row.get('note', '')
                            if '(' in note and '/' in note:
                                try:
                                    aggregated_in_parent = int(note.split('(')[1].split('/')[0])
                                    segment_counts[segment] = segment_counts.get(segment, 0) + aggregated_in_parent
                                except:
                                    segment_counts[segment] = segment_counts.get(segment, 0) + 1
                            else:
                                segment_counts[segment] = segment_counts.get(segment, 0) + 1

                        elif row['aggregated'] == 'No':
                            # Kategorie leaf - każda liczy się jako 1 dla swojego segmentu
                            segment_counts[segment] = segment_counts.get(segment, 0) + 1

                    if segment_counts:
                        # Sortuj segmenty według nazwy (numerycznie)
                        sorted_segments = sorted(segment_counts.items(),
                                               key=lambda x: int(''.join(filter(str.isdigit, str(x[0]))) or 0))

                        labels = [f"Segment {seg}" for seg, _ in sorted_segments]
                        sizes = [count for _, count in sorted_segments]

                        # Stonowane kolory - różne odcienie szarości i beżu
                        colors = ['#A8A8A8', '#C0C0C0', '#D2B48C', '#DDD8C7', '#B8B8B8', '#E6E6E6']
                        colors = colors[:len(sizes)]  # Dopasuj liczbę kolorów

                        fig, ax = plt.subplots(figsize=(4, 4))

                        wedges, texts, autotexts = ax.pie(
                            sizes,
                            labels=labels,
                            autopct='%1.1f%%',
                            startangle=90,
                            textprops={'fontsize': 10},
                            colors=colors
                        )

                        # Stonowany styl tekstu
                        for autotext in autotexts:
                            autotext.set_color('white')
                            autotext.set_fontweight('bold')
                            autotext.set_fontsize(9)

                        ax.axis('equal')
                        ax.set_title("Rozkład kategorii według segmentów", fontsize=11, pad=15)
                        st.pyplot(fig)

                        # Tabela ze szczegółami
                        st.markdown("**Szczegóły segmentów:**")
                        total_categories = sum(sizes)
                        summary_data = []

                        for (seg, count), label in zip(sorted_segments, labels):
                            summary_data.append({
                                'Segment': label,
                                'Liczba kategorii': count,
                                'Procent': f"{(count/total_categories*100):.1f}%"
                            })

                        summary_data.append({
                            'Segment': 'Razem',
                            'Liczba kategorii': total_categories,
                            'Procent': '100.0%'
                        })

                        summary_df = pd.DataFrame(summary_data)
                        st.dataframe(summary_df, use_container_width=True, hide_index=True)
                    else:
                        st.info("Brak danych do wykresu.")
                else:
                    st.info("Brak danych do wykresu.")
        else:
            st.info("Brak danych dla tej kategorii.")



    csv = result_df.to_csv(index=False).encode('utf-8')
    st.download_button("📥 Pobierz CSV", csv, "raport_segmentowy.csv", "text/csv")

except FileNotFoundError:
    st.error("❌ Nie znaleziono pliku `segments.csv` w katalogu skryptu.")
except Exception as e:
    st.error(f"❌ Błąd przetwarzania danych: {e}")
