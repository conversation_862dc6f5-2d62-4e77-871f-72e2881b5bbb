# 📊 Raport Segmentów Cenowych v2 - Hierarchiczne Filtrowanie

## 📋 Opis Programu

Program **Raport Segmentów Cenowych v2** to zaawansowana aplikacja Streamlit do analizy hierarchicznej kategorii produktów i ich przypisania do segmentów cenowych. Aplikacja implementuje inteligentny system filtrowania, który automatycznie agreguje kategorie na podstawie pokrycia segmentów cenowych, umożliwiając efektywne zarządzanie strukturą kategorii w e-commerce.

## 🎯 Główne Funkcje

### 1. **Hierarchiczne Filtrowanie Kategorii**
- **Main Categories**: Agregacja głównych kategorii przy wysokim pokryciu segmentu
- **Parent Categories**: Agregacja kategorii nadrzędnych przy średnim pokryciu
- **Leaf Categories**: Pozostałe kategorie końcowe z uwzględnieniem progów kliknięć

### 2. **Wielopoziomowa Analiza**
- Analiza pokrycia segmentów cenowych na trzech poziomach hierarchii
- Automatyczne wykrywanie kategorii o wysokim ruchu
- Identyfikacja kategorii wymagających weryfikacji

### 3. **Interaktywne Widoki Danych**
- Szczegółowa tabela z wszystkimi kategoriami
- Pivot table z segmentami jako kolumnami
- Drilldown analysis dla wybranych kategorii
- Wykresy kołowe rozkładu segmentów

### 4. **Eksport i Raportowanie**
- Eksport CSV z przetworzonymi danymi
- Raport tekstowy z listami kategorii w dwóch językach
- Podsumowania statystyczne

## 📁 Struktura Pliku Źródłowego (segments.csv)

### Wymagane Kolumny:

| Kolumna | Typ | Opis |
|---------|-----|------|
| `country_code` | string | Kod kraju (se, no, fi, dk, gb, fr, nz) |
| `category_id` | int/string | Unikalny identyfikator kategorii leaf |
| `category_name` | string | Lokalna nazwa kategorii leaf |
| `en_category_name` | string | Angielska nazwa kategorii leaf |
| `path` | string | Pełna ścieżka kategorii (format: id1\|id2\|id3¤nazwa1\|nazwa2\|nazwa3) |
| `visible` | boolean | Czy kategoria jest widoczna (TRUE/FALSE) |
| `status` | string | Status techniczny kategorii |
| `id_part` | string | Część path zawierająca tylko ID (przed znakiem ¤) |
| `main_category_id` | int | ID kategorii głównej |
| `en_main_category_name` | string | Angielska nazwa głównej kategorii |
| `main_category_name` | string | Lokalna nazwa głównej kategorii |
| `parent_id` | int | ID kategorii nadrzędnej |
| `en_parent_name` | string | Angielska nazwa kategorii parent |
| `parent_name` | string | Lokalna nazwa kategorii parent |
| `final_price_segment` | string | Segment cenowy (np. "Segment 10") |
| `clicks` | int | Liczba kliknięć kategorii |

### Przykład Struktury Danych:
```
main_category (Electronics) 
├── parent_category (Audio Equipment)
│   ├── leaf_category (Headphones)
│   ├── leaf_category (Speakers)
│   └── leaf_category (Amplifiers)
└── parent_category (Computer Parts)
    ├── leaf_category (CPUs)
    └── leaf_category (Graphics Cards)
```

## ⚙️ Parametry Filtrowania

### 1. **Podstawowe Filtry**
- **🌍 Kraj**: Wybór kraju do analizy
- **🌐 Język**: Angielski lub lokalny dla nazw kategorii

### 2. **Progi Agregacji**

#### **🏢 Min. pokrycie main (%)** - domyślnie 80%
- Sprawdza czy wszystkie podkategorie (parent + leaf) mają ten sam segment cenowy
- **Jeśli ≥80%** → pokazuje tylko main category, usuwa wszystkie podkategorie
- **Przykład**: Jeśli "Electronics" ma 10 podkategorii i 8 z nich jest w Segment 10 (80%), to pokazuje tylko "Electronics"

#### **📁 Min. pokrycie parent (%)** - domyślnie 50%
- Sprawdza czy kategorie leaf pod danym parent mają ten sam segment
- **Jeśli ≥50%** → pokazuje tylko parent category, usuwa zagregowane leaf
- **Przykład**: Jeśli "Audio Equipment" ma 5 kategorii leaf i 3 z nich jest w Segment 10 (60%), to pokazuje "Audio Equipment" + 2 wyjątki

#### **📊 Min. kategorii w parent** - domyślnie 3
- Minimalna liczba kategorii leaf wymagana do rozważenia agregacji parent
- Parent z mniej niż 3 kategoriami nie będzie agregowany

#### **🔥 Próg wysokich kliknięć** - domyślnie 100,000
- Kategorie powyżej tego progu są pokazywane mimo agregacji
- Zapewnia widoczność kategorii o wysokim ruchu

#### **📉 Próg niskich kliknięć** - domyślnie 1,000
- Kategorie poniżej tego progu są oznaczane do weryfikacji
- Pomaga identyfikować kategorie o małym znaczeniu

## 📊 Widoki i Tabele

### 1. **🧾 Szczegółowy Widok**
Główna tabela zawierająca wszystkie przetworzone kategorie:

| Kolumna | Opis |
|---------|------|
| `category_id` | ID kategorii (main_X, parent_X, lub oryginalne) |
| `category_name` | Nazwa w wybranym języku |
| `level` | Poziom hierarchii (main/parent/leaf) |
| `segment_name` | Przypisany segment cenowy |
| `clicks` | Suma kliknięć |
| `coverage` | Pokrycie segmentu (dla agregowanych) |
| `note` | Szczegóły agregacji |
| `categories_count` | Liczba zagregowanych kategorii |

### 2. **🧮 Widok Segmentów jako Kolumny**
Pivot table z kategoriami pogrupowanymi według segmentów:
- **Kolumny**: Segmenty cenowe (Segment 1, Segment 2, ...)
- **Wiersze**: Kategorie z oznaczeniami poziomów
- **Kolorowanie**:
  - 🔵 Niebieskie tło: Main categories (🏢)
  - 🟢 Zielone tło: Parent categories (📁)
  - 🔴 Czerwone tło: High clicks categories (🔥)
  - 🟡 Żółte tło: Low clicks categories (⚠️)

### 3. **🔎 Analiza Szczegółowa Kategorii**
Drilldown analysis dla wybranej kategorii:
- **Informacje podstawowe**: poziom, segment, kliki, pokrycie
- **Lista podkategorii**: w wybranym języku
- **Wykres kołowy**: rozkład podkategorii według segmentów
- **Tabela szczegółów**: statystyki segmentów

### 4. **📊 Analiza Rozkładu Segmentów**
Podsumowanie wszystkich segmentów:
- Łączne kliki na segment
- Liczba kategorii na segment
- Rozkład poziomów hierarchii

## 🔄 Algorytm Hierarchicznego Filtrowania

### Krok 1: Analiza Main Categories
```
FOR każda main_category:
    pokrycie = dominant_segment_count / total_categories
    IF pokrycie >= main_coverage_threshold:
        DODAJ main_category do wyników
        OZNACZ wszystkie podkategorie jako przetworzone
        WYJĄTEK: zachowaj kategorie z clicks >= high_clicks_threshold
```

### Krok 2: Analiza Parent Categories
```
FOR każdy parent (z nieprzetworzonych):
    IF liczba_kategorii >= min_parent_categories:
        pokrycie = dominant_segment_count / total_leaf_categories
        IF pokrycie >= parent_coverage_threshold:
            DODAJ parent_category do wyników
            OZNACZ zagregowane leaf jako przetworzone
            WYJĄTEK: zachowaj kategorie z clicks >= high_clicks_threshold
```

### Krok 3: Pozostałe Kategorie
```
FOR każdą nieprzetworzną kategorię:
    DODAJ jako leaf_category
    OZNACZ powód: high_clicks / remaining / low_clicks
```

## 📥 Eksport Danych

### 1. **CSV Export**
- Pełna tabela wyników w formacie CSV
- Wszystkie kolumny z przetworzonymi danymi
- Nazwa pliku: `raport_hierarchiczny.csv`

### 2. **Raport Tekstowy**
Strukturyzowany raport zawierający:
- **Nagłówek**: data, kraj, parametry filtrowania
- **Podsumowanie**: statystyki kategorii i kliknięć
- **Rozkład segmentów**: liczba kategorii i kliknięć na segment
- **Lista EN**: kategorie w języku angielskim pogrupowane według segmentów
- **Lista lokalna**: kategorie w języku lokalnym pogrupowane według segmentów
- Nazwa pliku: `raport_segmentow_{kraj}_{data_czas}.txt`

## 🛠️ Wymagania Techniczne

### Zależności Python:
```python
pandas>=1.5.0
streamlit>=1.28.0
matplotlib>=3.6.0
```

### Instalacja:
```bash
pip install pandas streamlit matplotlib
```

### Uruchomienie:
```bash
streamlit run segment_report_v2.py
```

## 📈 Przypadki Użycia

### 1. **Optymalizacja Struktury Kategorii**
- Identyfikacja kategorii do agregacji
- Uproszczenie nawigacji dla użytkowników
- Redukcja złożoności zarządzania

### 2. **Analiza Segmentacji Cenowej**
- Weryfikacja poprawności przypisań segmentów
- Wykrywanie niespójności w segmentacji
- Optymalizacja strategii cenowej

### 3. **Zarządzanie Produktem**
- Priorytetyzacja kategorii według ruchu
- Identyfikacja kategorii wymagających uwagi
- Planowanie rozwoju asortymentu

## 🔍 Interpretacja Wyników

### Oznaczenia w Tabelach:
- **🏢 Main**: Kategoria główna (zagregowana)
- **📁 Parent**: Kategoria nadrzędna (zagregowana)
- **📄 Leaf**: Kategoria końcowa
- **🔥**: Wysokie kliki (mimo agregacji)
- **⚠️**: Niskie kliki (do weryfikacji)
- **★**: Częściowa agregacja
- **✅**: Pełna agregacja (100%)

### Status Agregacji:
- **Yes**: 100% pokrycie segmentu
- **Partial**: Częściowe pokrycie (powyżej progu)
- **No**: Brak agregacji (poniżej progu)

## 📞 Wsparcie

Program został zaprojektowany dla zespołów e-commerce zajmujących się:
- Zarządzaniem katalogiem produktów
- Optymalizacją struktury kategorii
- Analizą segmentacji cenowej
- Strategią produktową

Dla pytań technicznych lub propozycji ulepszeń, skontaktuj się z zespołem rozwoju.
