import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt
import os
import io
from datetime import datetime

st.set_page_config(page_title="Raport segmentów cenowych v2", layout="wide")
st.title("📊 Raport segmentów cenowych v2 - Hierarchiczne filtrowanie")

# === FUNKCJA GENEROWANIA RAPORTU TEKSTOWEGO ===
def generate_text_report(result_df, filtered_df, selected_country, main_coverage, parent_coverage):
    """
    Generuje raport tekstowy z podsumowaniem i listą kategorii w obu językach
    """
    report_lines = []

    # Nagłówek raportu
    report_lines.append("=" * 80)
    report_lines.append("RAPORT SEGMENTÓW CENOWYCH")
    report_lines.append("=" * 80)
    report_lines.append(f"Data wygenerowania: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append(f"Kraj: {selected_country.upper()}")
    report_lines.append(f"Próg pokrycia main categories: {main_coverage:.0%}")
    report_lines.append(f"Próg pokrycia parent categories: {parent_coverage:.0%}")
    report_lines.append("")

    # Podsumowanie
    report_lines.append("PODSUMOWANIE:")
    report_lines.append("-" * 40)
    main_count = len(result_df[result_df['level'] == 'main'])
    parent_count = len(result_df[result_df['level'] == 'parent'])
    leaf_count = len(result_df[result_df['level'] == 'leaf'])
    total_clicks = result_df['clicks'].sum()

    report_lines.append(f"Main categories: {main_count}")
    report_lines.append(f"Parent categories: {parent_count}")
    report_lines.append(f"Leaf categories: {leaf_count}")
    report_lines.append(f"Łączne kliki: {total_clicks:,}")
    report_lines.append("")

    # Rozkład według segmentów
    segment_summary = result_df.groupby('segment_name').agg({
        'clicks': 'sum',
        'category_name': 'count'
    }).reset_index()
    segment_summary = segment_summary.sort_values('clicks', ascending=False)

    report_lines.append("ROZKŁAD WEDŁUG SEGMENTÓW:")
    report_lines.append("-" * 40)
    for _, row in segment_summary.iterrows():
        report_lines.append(f"{row['segment_name']}: {row['category_name']} kategorii, {row['clicks']:,} kliknięć")
    report_lines.append("")

    # Lista kategorii w języku angielskim
    report_lines.append("LISTA KATEGORII - JĘZYK ANGIELSKI:")
    report_lines.append("=" * 50)

    segments = sorted(result_df['segment_name'].unique(),
                     key=lambda x: int(''.join(filter(str.isdigit, str(x))) or 0))

    for segment in segments:
        segment_categories = result_df[result_df['segment_name'] == segment]
        report_lines.append(f"\n{segment}:")
        report_lines.append("-" * 20)

        for _, cat in segment_categories.iterrows():
            # Znajdź angielską nazwę w oryginalnych danych
            original_row = filtered_df[filtered_df['category_id'] == cat['category_id']]
            if not original_row.empty:
                if cat['level'] == 'main':
                    en_name = original_row.iloc[0].get('en_main_category_name', cat['category_name'])
                elif cat['level'] == 'parent':
                    en_name = original_row.iloc[0].get('en_parent_name', cat['category_name'])
                else:
                    en_name = original_row.iloc[0].get('en_category_name', cat['category_name'])
            else:
                en_name = cat['category_name']

            report_lines.append(f"  - {en_name}")

    # Lista kategorii w języku lokalnym
    country_lang_map = {
        'se': 'SZWEDZKI', 'no': 'NORWESKI', 'fi': 'FIŃSKI', 'dk': 'DUŃSKI',
        'gb': 'ANGIELSKI', 'fr': 'FRANCUSKI', 'nz': 'ANGIELSKI'
    }
    local_lang = country_lang_map.get(selected_country.lower(), 'LOKALNY')

    report_lines.append(f"\n\nLISTA KATEGORII - JĘZYK {local_lang}:")
    report_lines.append("=" * 50)

    for segment in segments:
        segment_categories = result_df[result_df['segment_name'] == segment]
        report_lines.append(f"\n{segment}:")
        report_lines.append("-" * 20)

        for _, cat in segment_categories.iterrows():
            # Znajdź lokalną nazwę w oryginalnych danych
            original_row = filtered_df[filtered_df['category_id'] == cat['category_id']]
            if not original_row.empty:
                if cat['level'] == 'main':
                    local_name = original_row.iloc[0].get('main_category_name', cat['category_name'])
                elif cat['level'] == 'parent':
                    local_name = original_row.iloc[0].get('parent_name', cat['category_name'])
                else:
                    local_name = original_row.iloc[0].get('category_name', cat['category_name'])
            else:
                local_name = cat['category_name']

            report_lines.append(f"  - {local_name}")

    return "\n".join(report_lines)

# === NOWA LOGIKA HIERARCHICZNEGO FILTROWANIA ===
def hierarchical_filter_categories(df, main_coverage_threshold=0.8, parent_coverage_threshold=0.5, min_parent_categories=3,
                                 high_clicks_threshold=100000, low_clicks_threshold=1000, language="Angielski"):
    """
    Nowa logika filtrowania kategorii według hierarchii:
    1. Main categories - sprawdź pokrycie segmentu cenowego
    2. Parent categories - pokrycie >50% i min 3 kategorie leaf
    3. Pozostałe kategorie - z uwzględnieniem progów kliknięć
    """
    
    result_categories = []
    processed_categories = set()  # Śledzenie przetworzonych kategorii
    
    # === KROK 1: MAIN CATEGORIES ===
    st.write("🔄 Krok 1: Analiza głównych kategorii...")
    
    # Grupuj według main_category_id
    main_groups = df.groupby('main_category_id')
    
    for main_id, main_group in main_groups:
        # Sprawdź pokrycie segmentu cenowego dla main category
        segment_counts = main_group['final_price_segment'].value_counts()
        total_categories = len(main_group)
        
        if len(segment_counts) > 0:
            dominant_segment = segment_counts.index[0]
            dominant_count = segment_counts.iloc[0]
            coverage = dominant_count / total_categories
            
            # Jeśli main category ma dominujący segment, dodaj tylko main
            if coverage >= main_coverage_threshold:
                sample = main_group.iloc[0]
                main_label = sample.get('en_main_category_name', 'UNKNOWN') if language == "Angielski" else sample.get('main_category_name', 'UNKNOWN')
                
                result_categories.append({
                    'category_id': str(f"main_{main_id}"),
                    'category_name': str(main_label),
                    'level': 'main',
                    'segment_name': str(dominant_segment),
                    'clicks': int(main_group['clicks'].sum()),
                    'coverage': f"{coverage:.1%}",
                    'note': f"Main category - {coverage:.1%} w {dominant_segment}",
                    'country_code': str(sample.get('country_code', 'XX')),
                    'categories_count': int(total_categories)
                })
                
                # Oznacz wszystkie kategorie z tej main jako przetworzone (z wyjątkiem high-clicks)
                for _, row in main_group.iterrows():
                    if row['clicks'] < high_clicks_threshold:
                        processed_categories.add(row['category_id'])
                
                continue
    
    # === KROK 2: PARENT CATEGORIES ===
    st.write("🔄 Krok 2: Analiza kategorii nadrzędnych...")
    
    # Pozostałe kategorie (nie przetworzone w kroku 1)
    remaining_df = df[~df['category_id'].isin(processed_categories)]
    
    if len(remaining_df) > 0:
        # Grupuj według parent_id
        parent_groups = remaining_df.groupby('parent_id')
        
        for parent_id, parent_group in parent_groups:
            if len(parent_group) < min_parent_categories:
                continue  # Pomiń parenty z mniej niż min_parent_categories
                
            # Sprawdź pokrycie segmentu cenowego dla parent
            segment_counts = parent_group['final_price_segment'].value_counts()
            total_categories = len(parent_group)
            
            if len(segment_counts) > 0:
                dominant_segment = segment_counts.index[0]
                dominant_count = segment_counts.iloc[0]
                coverage = dominant_count / total_categories
                
                # Dodaj parent jeśli pokrycie >= threshold
                if coverage >= parent_coverage_threshold:
                    sample = parent_group.iloc[0]
                    parent_label = sample.get('en_parent_name', 'UNKNOWN') if language == "Angielski" else sample.get('parent_name', 'UNKNOWN')
                    
                    result_categories.append({
                        'category_id': str(f"parent_{parent_id}"),
                        'category_name': str(parent_label),
                        'level': 'parent',
                        'segment_name': str(dominant_segment),
                        'clicks': int(parent_group['clicks'].sum()),
                        'coverage': f"{coverage:.1%}",
                        'note': f"Parent category - {coverage:.1%} w {dominant_segment}",
                        'country_code': str(sample.get('country_code', 'XX')),
                        'categories_count': int(total_categories)
                    })
                    
                    # Oznacz kategorie z tego parent jako przetworzone (z wyjątkiem high-clicks)
                    for _, row in parent_group.iterrows():
                        if row['clicks'] < high_clicks_threshold:
                            processed_categories.add(row['category_id'])
    
    # === KROK 3: POZOSTAŁE KATEGORIE ===
    st.write("🔄 Krok 3: Analiza pozostałych kategorii...")
    
    # Ostatecznie pozostałe kategorie
    final_remaining_df = df[~df['category_id'].isin(processed_categories)]
    
    for _, row in final_remaining_df.iterrows():
        category_label = row.get('en_category_name', 'UNKNOWN') if language == "Angielski" else row.get('category_name', 'UNKNOWN')
        clicks = row.get('clicks', 0)
        
        # Określ powód dodania kategorii
        if clicks >= high_clicks_threshold:
            note = f"High clicks ({clicks:,}) - pokazana mimo agregacji parent"
        elif clicks >= low_clicks_threshold:
            note = f"Pozostała kategoria ({clicks:,} kliknięć)"
        else:
            note = f"Niska liczba kliknięć ({clicks:,}) - do weryfikacji"
        
        result_categories.append({
            'category_id': str(row['category_id']),
            'category_name': str(category_label),
            'level': 'leaf',
            'segment_name': str(row.get('final_price_segment', 'UNKNOWN')),
            'clicks': int(clicks),
            'coverage': 'N/A',
            'note': str(note),
            'country_code': str(row.get('country_code', 'XX')),
            'categories_count': 1
        })
    
    return pd.DataFrame(result_categories)


# === WGRYWANIE DANYCH ===
csv_path = os.path.join(os.path.dirname(__file__), 'segments.csv')

try:
    df = pd.read_csv(csv_path)
    
    df['clicks'] = pd.to_numeric(df['clicks'], errors='coerce')
    df = df[df['clicks'].notna()]
    df = df[df['final_price_segment'].notna()]  # Usuń puste segmenty
    
    st.success("✅ Załadowano dane z pliku `segments.csv`")
    
    # === FILTRY ===
    flag_map = {
        'se': '🇸🇪', 'no': '🇳🇴', 'fi': '🇫🇮', 'dk': '🇩🇰',
        'gb': '🇬🇧', 'fr': '🇫🇷', 'nz': '🇳🇿'
    }
    
    country_codes = sorted(df['country_code'].dropna().unique())
    default_country = "se"
    default_index = country_codes.index(default_country) if default_country in country_codes else 0
    country_labels = [f"{flag_map.get(code.lower(), '')} {code.upper()}" for code in country_codes]
    
    # Interfejs użytkownika
    col1, col2 = st.columns(2)
    with col1:
        selected_label = st.selectbox("🌍 Wybierz kraj", options=country_labels, index=default_index)
        selected_country = selected_label.split()[-1].lower()
    
    with col2:
        language = st.selectbox("🌐 Język wyświetlanych nazw", ["Angielski", "Lokalny"])
    
    # Zaawansowane filtry
    st.markdown("### ⚙️ Parametry filtrowania")

    # Wyjaśnienie progów
    with st.expander("ℹ️ Jak działają progi procentowe?"):
        st.markdown("""
        **Main Category (Główna kategoria):**
        - Sprawdza czy wszystkie podkategorie (parent + leaf) mają ten sam segment cenowy
        - Jeśli ≥80% kategorii ma ten sam segment → pokazuje tylko main category
        - Usuwa wszystkie podkategorie z listy (oszczędza miejsce)

        **Parent Category (Kategoria nadrzędna):**
        - Sprawdza czy kategorie leaf pod danym parent mają ten sam segment
        - Jeśli ≥50% leaf ma ten sam segment → pokazuje tylko parent category
        - Usuwa zagregowane leaf z listy, ale zostawia wyjątki

        **Przykład:** Jeśli parent ma 5 kategorii leaf i 4 z nich są w Segment 10,
        to przy progu 80% (4/5=80%) parent zostanie zagregowany.
        """)

    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        main_coverage = st.slider("🏢 Min. pokrycie main (%)", 0, 100, 40, step=5) / 100
    with col2:
        parent_coverage = st.slider("� Min. pokrycie parent (%)", 0, 100, 50, step=5) / 100
    with col3:
        min_parent_cats = st.number_input("� Min. kategorii w parent", min_value=1, value=3, step=1)
    with col4:
        high_clicks = st.number_input("🔥 Próg wysokich kliknięć", min_value=1000, value=100000, step=10000)
    with col5:
        low_clicks = st.number_input("📉 Próg niskich kliknięć", min_value=0, value=1000, step=100)
    
    # Filtruj dane
    filtered_df = df[df['country_code'] == selected_country]
    
    if len(filtered_df) == 0:
        st.warning("⚠️ Brak danych dla wybranego kraju.")
        st.stop()
    
    # Zastosuj nową logikę filtrowania
    result_df = hierarchical_filter_categories(
        filtered_df,
        main_coverage_threshold=main_coverage,
        parent_coverage_threshold=parent_coverage,
        min_parent_categories=min_parent_cats,
        high_clicks_threshold=high_clicks,
        low_clicks_threshold=low_clicks,
        language=language
    )
    
    if len(result_df) == 0:
        st.warning("⚠️ Brak wyników po filtrowaniu hierarchicznym.")
        st.stop()
    
    # === WYŚWIETLANIE WYNIKÓW ===
    st.markdown("### 📋 Wyniki hierarchicznego filtrowania")
    
    # Statystyki
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        main_count = len(result_df[result_df['level'] == 'main'])
        st.metric("🏢 Main categories", main_count)
    with col2:
        parent_count = len(result_df[result_df['level'] == 'parent'])
        st.metric("📁 Parent categories", parent_count)
    with col3:
        leaf_count = len(result_df[result_df['level'] == 'leaf'])
        st.metric("📄 Leaf categories", leaf_count)
    with col4:
        total_clicks = result_df['clicks'].sum()
        st.metric("👆 Łączne kliki", f"{total_clicks:,}")
    
    # Główna tabela
    st.dataframe(result_df, use_container_width=True)

    # === WIDOK SEGMENTÓW JAKO KOLUMNY ===
    st.markdown("### 🧮 Widok segmentów jako kolumny")

    pivot = result_df.copy()
    pivot['segment_number'] = (
        pivot['segment_name'].astype(str).str.extract(r'(\d+)')[0].astype(float)
    )
    pivot = pivot.sort_values('segment_number')

    def format_label_v2(row):
        level = row.get("level", "")
        label = f"{row.get('category_name', '')} ({level})"
        coverage = row.get('coverage', '')

        if level == "main":
            label += f" 🏢 {coverage}"
        elif level == "parent":
            label += f" 📁 {coverage}"
        elif "High clicks" in str(row.get('note', '')):
            label += " 🔥"
        elif "Niska liczba" in str(row.get('note', '')):
            label += " ⚠️"

        return label

    pivot['label'] = pivot.apply(format_label_v2, axis=1)

    pivot_grouped = (
        pivot.groupby('segment_name')['label']
        .apply(lambda labels: sorted(labels, key=lambda x: str(x).casefold()))
        .to_dict()
    )

    sorted_segments = sorted(
        pivot_grouped.keys(),
        key=lambda x: int(''.join(filter(str.isdigit, str(x))) or 0)
    )

    max_rows = max(len(v) for v in pivot_grouped.values()) if pivot_grouped else 0
    formatted_columns = {}
    for seg in sorted_segments:
        items = pivot_grouped.get(seg, [])
        formatted_columns[seg] = items + [''] * (max_rows - len(items))

    final_pivot_df = pd.DataFrame(formatted_columns)

    def highlight_cells_v2(val):
        if isinstance(val, str):
            style = ""
            if "🏢" in val:  # Main
                style += "background-color: #e6f3ff; font-weight: bold;"
            elif "📁" in val:  # Parent
                style += "background-color: #f0f8e6;"
            elif "🔥" in val:  # High clicks
                style += "background-color: #ffe6e6;"
            elif "⚠️" in val:  # Low clicks
                style += "background-color: #fff4e6;"
            return style
        return ""

    styled_df = final_pivot_df.style.map(highlight_cells_v2)

    # Interaktywna tabela z możliwością kliknięcia
    st.markdown("**� Kliknij na kategorię w tabeli poniżej, aby automatycznie załadować szczegóły:**")

    # Użyj zwykłej tabeli bez stylowania dla interaktywności
    pivot_selection = st.dataframe(
        final_pivot_df,
        use_container_width=True,
        height=600,
        on_select="rerun",
        selection_mode="single-cell"
    )

    # === ANALIZA SZCZEGÓŁOWA KATEGORII ===
    st.markdown("### 🔎 Analiza szczegółowa kategorii")

    # Sprawdź czy użytkownik kliknął w tabelę
    selected_category = None
    if pivot_selection.selection.rows or pivot_selection.selection.columns:
        # Jeśli kliknięto w komórkę, spróbuj wyciągnąć nazwę kategorii
        if len(pivot_selection.selection.rows) > 0:
            selected_row = pivot_selection.selection.rows[0]
            if selected_row < len(final_pivot_df):
                # Znajdź pierwszą niepustą wartość w tym wierszu
                row_data = final_pivot_df.iloc[selected_row]
                for col_name, cell_value in row_data.items():
                    if pd.notna(cell_value) and str(cell_value).strip():
                        # Wyciągnij nazwę kategorii z tekstu (usuń oznaczenia)
                        category_text = str(cell_value)
                        if '(' in category_text:
                            selected_category = category_text.split('(')[0].strip()
                            # Usuń dodatkowe oznaczenia
                            selected_category = selected_category.replace('🏢', '').replace('📁', '').replace('📄', '').replace('🔥', '').replace('⚠️', '').strip()
                        break

    # Fallback do selectbox jeśli nie kliknięto lub nie znaleziono kategorii
    available_categories = result_df['category_name'].unique()
    if selected_category and selected_category in available_categories:
        default_index = list(available_categories).index(selected_category) + 1
        st.info(f"🎯 Automatycznie wybrano kategorię z tabeli: **{selected_category}**")
    else:
        default_index = 0
        selected_category = None

    manual_selection = st.selectbox("🔍 Lub wybierz kategorię ręcznie:",
                                   ['-- Wybierz kategorię --'] + list(available_categories),
                                   index=default_index)

    # Użyj ręcznego wyboru jeśli nie ma automatycznego
    if manual_selection != '-- Wybierz kategorię --':
        selected_category = manual_selection

    if selected_category and selected_category != '-- Wybierz kategorię --':
        # Znajdź wybraną kategorię
        selected_row = result_df[result_df['category_name'] == selected_category].iloc[0]

        st.markdown(f"### 📊 Szczegóły dla: **{selected_category}**")

        left_col, right_col = st.columns([3, 1])

        with left_col:
            st.subheader("🗂️ Informacje o kategorii")

            # Pokaż szczegóły wybranej kategorii
            details_df = pd.DataFrame([{
                'Właściwość': 'Nazwa kategorii',
                'Wartość': selected_row['category_name']
            }, {
                'Właściwość': 'Poziom',
                'Wartość': selected_row['level']
            }, {
                'Właściwość': 'Segment cenowy',
                'Wartość': selected_row['segment_name']
            }, {
                'Właściwość': 'Kliki',
                'Wartość': f"{selected_row['clicks']:,}"
            }, {
                'Właściwość': 'Pokrycie segmentu',
                'Wartość': selected_row['coverage']
            }, {
                'Właściwość': 'Liczba podkategorii',
                'Wartość': selected_row['categories_count']
            }, {
                'Właściwość': 'Uwagi',
                'Wartość': selected_row['note']
            }])

            st.dataframe(details_df, use_container_width=True, hide_index=True)

            # Jeśli to parent lub main, pokaż podkategorie
            if selected_row['level'] in ['parent', 'main']:
                st.subheader("📋 Podkategorie")

                if selected_row['level'] == 'parent':
                    # Znajdź wszystkie leaf pod tym parent
                    parent_id = selected_row['category_id'].replace('parent_', '')
                    subcategories = filtered_df[filtered_df['parent_id'].astype(str) == parent_id]
                elif selected_row['level'] == 'main':
                    # Znajdź wszystkie kategorie pod tym main
                    main_id = selected_row['category_id'].replace('main_', '')
                    subcategories = filtered_df[filtered_df['main_category_id'].astype(str) == main_id]

                if len(subcategories) > 0:
                    # Przygotuj dane podkategorii w wybranym języku
                    sub_display = subcategories.copy()

                    # Wybierz odpowiednie nazwy w zależności od języka
                    if language == "Angielski":
                        sub_display['display_name'] = sub_display['en_category_name']
                    else:
                        sub_display['display_name'] = sub_display['category_name']

                    # Przygotuj finalne kolumny
                    sub_display = sub_display[['display_name', 'final_price_segment', 'clicks']].copy()
                    sub_display.columns = ['Nazwa kategorii', 'Segment', 'Kliki']
                    sub_display = sub_display.sort_values('Kliki', ascending=False)
                    st.dataframe(sub_display, use_container_width=True, hide_index=True)
                else:
                    st.info("Brak podkategorii do wyświetlenia.")

        with right_col:
            st.subheader("📈 Rozkład segmentów")

            # Wykres kołowy dla podkategorii
            if selected_row['level'] in ['parent', 'main'] and len(subcategories) > 0:
                # Policz kategorie według segmentów
                segment_counts = subcategories['final_price_segment'].value_counts()

                if len(segment_counts) > 1:  # Tylko jeśli jest więcej niż 1 segment
                    labels = [f"Segment {seg.split()[-1]}" for seg in segment_counts.index]
                    sizes = segment_counts.values

                    # Stonowane kolory
                    colors = ['#A8A8A8', '#C0C0C0', '#D2B48C', '#DDD8C7', '#B8B8B8', '#E6E6E6']
                    colors = colors[:len(sizes)]

                    fig, ax = plt.subplots(figsize=(4, 4))

                    wedges, texts, autotexts = ax.pie(
                        sizes,
                        labels=labels,
                        autopct='%1.1f%%',
                        startangle=90,
                        textprops={'fontsize': 10},
                        colors=colors
                    )

                    # Stonowany styl tekstu
                    for autotext in autotexts:
                        autotext.set_color('white')
                        autotext.set_fontweight('bold')
                        autotext.set_fontsize(9)

                    ax.axis('equal')
                    ax.set_title("Rozkład podkategorii według segmentów", fontsize=11, pad=15)
                    st.pyplot(fig)

                    # Tabela ze szczegółami
                    st.markdown("**Szczegóły segmentów:**")
                    total_categories = sum(sizes)
                    summary_data = []

                    for segment, count in segment_counts.items():
                        summary_data.append({
                            'Segment': segment,
                            'Liczba kategorii': count,
                            'Procent': f"{(count/total_categories*100):.1f}%"
                        })

                    summary_df = pd.DataFrame(summary_data)
                    st.dataframe(summary_df, use_container_width=True, hide_index=True)
                else:
                    st.info("Wszystkie podkategorie w tym samym segmencie.")
            else:
                st.info("Brak podkategorii do analizy segmentów.")

    # === ANALIZA ROZKŁADU SEGMENTÓW ===
    st.markdown("### 📊 Analiza rozkładu segmentów")

    segment_analysis = result_df.groupby('segment_name').agg({
        'clicks': 'sum',
        'category_name': 'count',
        'level': lambda x: x.value_counts().to_dict()
    }).reset_index()

    segment_analysis.columns = ['Segment', 'Łączne kliki', 'Liczba kategorii', 'Rozkład poziomów']
    st.dataframe(segment_analysis, use_container_width=True)

    # === EKSPORT DANYCH ===
    st.markdown("### 📥 Eksport danych")

    col1, col2 = st.columns(2)

    with col1:
        # Eksport CSV
        csv = result_df.to_csv(index=False).encode('utf-8')
        st.download_button("� Pobierz CSV", csv, "raport_hierarchiczny.csv", "text/csv")

    with col2:
        # Eksport raportu tekstowego
        if st.button("📄 Generuj raport tekstowy"):
            report_text = generate_text_report(result_df, filtered_df, selected_country, main_coverage, parent_coverage)

            # Zapisz do pliku tekstowego
            report_bytes = report_text.encode('utf-8')
            st.download_button(
                "📥 Pobierz raport tekstowy",
                report_bytes,
                f"raport_segmentow_{selected_country}_{datetime.now().strftime('%Y%m%d_%H%M')}.txt",
                "text/plain"
            )

            # Pokaż podgląd raportu
            with st.expander("👁️ Podgląd raportu"):
                st.text(report_text)

except FileNotFoundError:
    st.error("❌ Nie znaleziono pliku `segments.csv` w katalogu skryptu.")
except Exception as e:
    st.error(f"❌ Błąd przetwarzania danych: {e}")
