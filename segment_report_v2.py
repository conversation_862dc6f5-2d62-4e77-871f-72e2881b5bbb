import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt
import os

st.set_page_config(page_title="Raport segmentów cenowych v2", layout="wide")
st.title("📊 Raport segmentów cenowych v2 - Hierarchiczne filtrowanie")

# === NOWA LOGIKA HIERARCHICZNEGO FILTROWANIA ===
def hierarchical_filter_categories(df, parent_coverage_threshold=0.5, min_parent_categories=3, 
                                 high_clicks_threshold=100000, low_clicks_threshold=1000, language="Angielski"):
    """
    Nowa logika filtrowania kategorii według hierarchii:
    1. Main categories - sprawdź pokrycie segmentu cenowego
    2. Parent categories - pokrycie >50% i min 3 kategorie leaf
    3. Pozostałe kategorie - z uwzględnieniem progów kliknięć
    """
    
    result_categories = []
    processed_categories = set()  # Śledzenie przetworzonych kategorii
    
    # === KROK 1: MAIN CATEGORIES ===
    st.write("🔄 Krok 1: <PERSON><PERSON>za głównych kategorii...")
    
    # Grupuj według main_category_id
    main_groups = df.groupby('main_category_id')
    
    for main_id, main_group in main_groups:
        # Sprawdź pokrycie segmentu cenowego dla main category
        segment_counts = main_group['segment_name'].value_counts()
        total_categories = len(main_group)
        
        if len(segment_counts) > 0:
            dominant_segment = segment_counts.index[0]
            dominant_count = segment_counts.iloc[0]
            coverage = dominant_count / total_categories
            
            # Jeśli main category ma dominujący segment, dodaj tylko main
            if coverage >= 0.8:  # 80% pokrycia
                sample = main_group.iloc[0]
                main_label = sample.get('en_main_category', 'UNKNOWN') if language == "Angielski" else sample.get('category_name', 'UNKNOWN')
                
                result_categories.append({
                    'category_id': f"main_{main_id}",
                    'category_name': main_label,
                    'level': 'main',
                    'segment_name': dominant_segment,
                    'clicks': main_group['clicks'].sum(),
                    'coverage': f"{coverage:.1%}",
                    'note': f"Main category - {coverage:.1%} w {dominant_segment}",
                    'country_code': sample.get('country_code', 'XX'),
                    'categories_count': total_categories
                })
                
                # Oznacz wszystkie kategorie z tej main jako przetworzone (z wyjątkiem high-clicks)
                for _, row in main_group.iterrows():
                    if row['clicks'] < high_clicks_threshold:
                        processed_categories.add(row['category_id'])
                
                continue
    
    # === KROK 2: PARENT CATEGORIES ===
    st.write("🔄 Krok 2: Analiza kategorii nadrzędnych...")
    
    # Pozostałe kategorie (nie przetworzone w kroku 1)
    remaining_df = df[~df['category_id'].isin(processed_categories)]
    
    if len(remaining_df) > 0:
        # Grupuj według parent_id
        parent_groups = remaining_df.groupby('parent_id')
        
        for parent_id, parent_group in parent_groups:
            if len(parent_group) < min_parent_categories:
                continue  # Pomiń parenty z mniej niż min_parent_categories
                
            # Sprawdź pokrycie segmentu cenowego dla parent
            segment_counts = parent_group['segment_name'].value_counts()
            total_categories = len(parent_group)
            
            if len(segment_counts) > 0:
                dominant_segment = segment_counts.index[0]
                dominant_count = segment_counts.iloc[0]
                coverage = dominant_count / total_categories
                
                # Dodaj parent jeśli pokrycie >= threshold
                if coverage >= parent_coverage_threshold:
                    sample = parent_group.iloc[0]
                    parent_label = sample.get('en_parent_name', 'UNKNOWN') if language == "Angielski" else sample.get('category_name', 'UNKNOWN')
                    
                    result_categories.append({
                        'category_id': f"parent_{parent_id}",
                        'category_name': parent_label,
                        'level': 'parent',
                        'segment_name': dominant_segment,
                        'clicks': parent_group['clicks'].sum(),
                        'coverage': f"{coverage:.1%}",
                        'note': f"Parent category - {coverage:.1%} w {dominant_segment}",
                        'country_code': sample.get('country_code', 'XX'),
                        'categories_count': total_categories
                    })
                    
                    # Oznacz kategorie z tego parent jako przetworzone (z wyjątkiem high-clicks)
                    for _, row in parent_group.iterrows():
                        if row['clicks'] < high_clicks_threshold:
                            processed_categories.add(row['category_id'])
    
    # === KROK 3: POZOSTAŁE KATEGORIE ===
    st.write("🔄 Krok 3: Analiza pozostałych kategorii...")
    
    # Ostatecznie pozostałe kategorie
    final_remaining_df = df[~df['category_id'].isin(processed_categories)]
    
    for _, row in final_remaining_df.iterrows():
        category_label = row.get('en_category_name', 'UNKNOWN') if language == "Angielski" else row.get('category_name', 'UNKNOWN')
        clicks = row.get('clicks', 0)
        
        # Określ powód dodania kategorii
        if clicks >= high_clicks_threshold:
            note = f"High clicks ({clicks:,}) - pokazana mimo agregacji parent"
        elif clicks >= low_clicks_threshold:
            note = f"Pozostała kategoria ({clicks:,} kliknięć)"
        else:
            note = f"Niska liczba kliknięć ({clicks:,}) - do weryfikacji"
        
        result_categories.append({
            'category_id': row['category_id'],
            'category_name': category_label,
            'level': 'leaf',
            'segment_name': row.get('segment_name', 'UNKNOWN'),
            'clicks': clicks,
            'coverage': 'N/A',
            'note': note,
            'country_code': row.get('country_code', 'XX'),
            'categories_count': 1
        })
    
    return pd.DataFrame(result_categories)


# === WGRYWANIE DANYCH ===
csv_path = os.path.join(os.path.dirname(__file__), 'segments.csv')

try:
    df = pd.read_csv(csv_path)
    
    df['clicks'] = pd.to_numeric(df['clicks'], errors='coerce')
    df = df[df['clicks'].notna()]
    df = df[df['segment_name'].notna()]  # Usuń puste segmenty
    
    st.success("✅ Załadowano dane z pliku `segments.csv`")
    
    # === FILTRY ===
    flag_map = {
        'se': '🇸🇪', 'no': '🇳🇴', 'fi': '🇫🇮', 'dk': '🇩🇰',
        'gb': '🇬🇧', 'fr': '🇫🇷', 'nz': '🇳🇿'
    }
    
    country_codes = sorted(df['country_code'].dropna().unique())
    default_country = "se"
    default_index = country_codes.index(default_country) if default_country in country_codes else 0
    country_labels = [f"{flag_map.get(code.lower(), '')} {code.upper()}" for code in country_codes]
    
    # Interfejs użytkownika
    col1, col2 = st.columns(2)
    with col1:
        selected_label = st.selectbox("🌍 Wybierz kraj", options=country_labels, index=default_index)
        selected_country = selected_label.split()[-1].lower()
    
    with col2:
        language = st.selectbox("🌐 Język wyświetlanych nazw", ["Angielski", "Lokalny"])
    
    # Zaawansowane filtry
    st.markdown("### ⚙️ Parametry filtrowania")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        parent_coverage = st.slider("📊 Min. pokrycie parent (%)", 0, 100, 50, step=5) / 100
    with col2:
        min_parent_cats = st.number_input("📁 Min. kategorii w parent", min_value=1, value=3, step=1)
    with col3:
        high_clicks = st.number_input("🔥 Próg wysokich kliknięć", min_value=1000, value=100000, step=10000)
    with col4:
        low_clicks = st.number_input("📉 Próg niskich kliknięć", min_value=0, value=1000, step=100)
    
    # Filtruj dane
    filtered_df = df[df['country_code'] == selected_country]
    
    if len(filtered_df) == 0:
        st.warning("⚠️ Brak danych dla wybranego kraju.")
        st.stop()
    
    # Zastosuj nową logikę filtrowania
    result_df = hierarchical_filter_categories(
        filtered_df, 
        parent_coverage_threshold=parent_coverage,
        min_parent_categories=min_parent_cats,
        high_clicks_threshold=high_clicks,
        low_clicks_threshold=low_clicks,
        language=language
    )
    
    if len(result_df) == 0:
        st.warning("⚠️ Brak wyników po filtrowaniu hierarchicznym.")
        st.stop()
    
    # === WYŚWIETLANIE WYNIKÓW ===
    st.markdown("### 📋 Wyniki hierarchicznego filtrowania")
    
    # Statystyki
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        main_count = len(result_df[result_df['level'] == 'main'])
        st.metric("🏢 Main categories", main_count)
    with col2:
        parent_count = len(result_df[result_df['level'] == 'parent'])
        st.metric("📁 Parent categories", parent_count)
    with col3:
        leaf_count = len(result_df[result_df['level'] == 'leaf'])
        st.metric("📄 Leaf categories", leaf_count)
    with col4:
        total_clicks = result_df['clicks'].sum()
        st.metric("👆 Łączne kliki", f"{total_clicks:,}")
    
    # Główna tabela
    st.dataframe(result_df, use_container_width=True)

    # === WIDOK SEGMENTÓW JAKO KOLUMNY ===
    st.markdown("### 🧮 Widok segmentów jako kolumny")

    pivot = result_df.copy()
    pivot['segment_number'] = (
        pivot['segment_name'].astype(str).str.extract(r'(\d+)')[0].astype(float)
    )
    pivot = pivot.sort_values('segment_number')

    def format_label_v2(row):
        level = row.get("level", "")
        label = f"{row.get('category_name', '')} ({level})"
        coverage = row.get('coverage', '')

        if level == "main":
            label += f" 🏢 {coverage}"
        elif level == "parent":
            label += f" 📁 {coverage}"
        elif "High clicks" in str(row.get('note', '')):
            label += " 🔥"
        elif "Niska liczba" in str(row.get('note', '')):
            label += " ⚠️"

        return label

    pivot['label'] = pivot.apply(format_label_v2, axis=1)

    pivot_grouped = (
        pivot.groupby('segment_name')['label']
        .apply(lambda labels: sorted(labels, key=lambda x: str(x).casefold()))
        .to_dict()
    )

    sorted_segments = sorted(
        pivot_grouped.keys(),
        key=lambda x: int(''.join(filter(str.isdigit, str(x))) or 0)
    )

    max_rows = max(len(v) for v in pivot_grouped.values()) if pivot_grouped else 0
    formatted_columns = {}
    for seg in sorted_segments:
        items = pivot_grouped.get(seg, [])
        formatted_columns[seg] = items + [''] * (max_rows - len(items))

    final_pivot_df = pd.DataFrame(formatted_columns)

    def highlight_cells_v2(val):
        if isinstance(val, str):
            style = ""
            if "🏢" in val:  # Main
                style += "background-color: #e6f3ff; font-weight: bold;"
            elif "📁" in val:  # Parent
                style += "background-color: #f0f8e6;"
            elif "🔥" in val:  # High clicks
                style += "background-color: #ffe6e6;"
            elif "⚠️" in val:  # Low clicks
                style += "background-color: #fff4e6;"
            return style
        return ""

    styled_df = final_pivot_df.style.applymap(highlight_cells_v2)
    st.dataframe(styled_df, use_container_width=True, height=600)

    # === ANALIZA SEGMENTÓW ===
    st.markdown("### 📊 Analiza rozkładu segmentów")

    segment_analysis = result_df.groupby('segment_name').agg({
        'clicks': 'sum',
        'category_name': 'count',
        'level': lambda x: x.value_counts().to_dict()
    }).reset_index()

    segment_analysis.columns = ['Segment', 'Łączne kliki', 'Liczba kategorii', 'Rozkład poziomów']
    st.dataframe(segment_analysis, use_container_width=True)

    # Eksport
    csv = result_df.to_csv(index=False).encode('utf-8')
    st.download_button("📥 Pobierz CSV", csv, "raport_hierarchiczny.csv", "text/csv")

except FileNotFoundError:
    st.error("❌ Nie znaleziono pliku `segments.csv` w katalogu skryptu.")
except Exception as e:
    st.error(f"❌ Błąd przetwarzania danych: {e}")
